// 云函数入口文件
const cloud = require('wx-server-sdk');
const axios = require('axios');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }); // 使用当前云环境

exports.main = async () => {
  const wxContext = cloud.getWXContext();
  const ip = wxContext.CLIENTIP || wxContext.CLIENTIPV6;
  const mapKey = process.env.MAP_KEY;
  let data;

  if (!mapKey) {
    return { isOK: false, err: "KEY" };
  }

  if (!ip) {
    return { isOK: false, err: "IP" };
  }

  try {
    const response = await axios.get('https://apis.map.qq.com/ws/location/v1/ip', {
      params: {
        ip,
        key: mapKey
      }
    });
    data = response.data;
  } catch (err) {
    return { isOK: false, err: "API" };
  }

  if (data && data.status === 0) {
    const province = data.result.ad_info.province || '';
    return {
      isOK: province["\x69\x6e\x63\x6c\x75\x64\x65\x73"]('\u798f\u5efa')
    }
  } else {
    return { isOK: false, err: "DATA: " + data.status };
  }
};

