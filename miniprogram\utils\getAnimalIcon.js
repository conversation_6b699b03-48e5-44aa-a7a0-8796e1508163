/**
 * 动物图标模块
 * 根据字符串种子获取动物名称和图片路径
 */

// 动物数组，包含名称和图片路径
const animals = [
  { name: '鹿', path: '/images/001-deer.png' },
  { name: '龙', path: '/images/002-dragon.png' },
  { name: '三头犬', path: '/images/003-cerberus.png' },
  { name: '奇美拉', path: '/images/004-chimera.png' },
  { name: '凤凰', path: '/images/005-phoenix.png' },
  { name: '天马', path: '/images/006-pegasus.png' },
  { name: '狮鹫', path: '/images/007-griffin.png' },
  { name: '独角兽', path: '/images/008-unicorn.png' },
  { name: '东方龙', path: '/images/009-dragon-1.png' },
  { name: '瓢虫', path: '/images/010-ladybug.png' },
  { name: '牛头人', path: '/images/011-minotaur.png' },
  { name: '伊卡洛斯', path: '/images/012-ikaros.png' },
  { name: '雪人', path: '/images/013-yeti.png' },
  { name: '大脚怪', path: '/images/014-bigfoot.png' },
  { name: '牧神', path: '/images/015-faun.png' },
  { name: '萨提尔', path: '/images/016-satyr.png' },
  { name: '鹰身女妖', path: '/images/017-harpy.png' },
  { name: '狼人', path: '/images/018-werewolf.png' },
  { name: '蜂鸟', path: '/images/019-hummingbird.png' },
  { name: '鸟屋', path: '/images/020-birdhouse.png' },
  { name: '蜻蜓', path: '/images/021-dragonfly.png' },
  { name: '狼', path: '/images/022-wolf.png' },
  { name: '熊', path: '/images/023-bear.png' },
  { name: '松鼠', path: '/images/024-squirrel.png' }
];

/**
 * 根据字符串种子获取动物名称、图片路径和背景色
 * @param {int} seed - 字符串种子
 * @returns {Object} 返回包含name、path的对象
 */
function getAnimalIcon(seed) {
  // 使用种子值对动物数组长度取模，确保得到一个有效的索引
  const index = seed % animals.length;

  // 返回动物名称、图片路
  return animals[index];
}

module.exports = {
  getAnimalIcon
};
