<view class="feed-container">
  <!-- 分区选择栏 (横向滑动) - 固定在顶部 -->
  <view class="tag-container-fixed">
    <scroll-view class="tag-container" scroll-x="true" enhanced="true" show-scrollbar="{{false}}">
      <view class="tag-scroll-content">
        <view wx:for="{{tagList}}" wx:key="id" class="tag-item {{currentTag === item.id ? 'tag-active' : ''}}"
          bindtap="switchTag" data-tag-id="{{item.id}}">
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 添加一个占位元素，防止内容被固定的分区选择栏遮挡 -->
  <view class="tag-container-placeholder"></view>

  <!-- 信息流列表 -->
  <view wx:for="{{feedList}}" wx:key="id" class="feed-item {{item.isNewPost ? 'new-post' : ''}}">
    <image class="avatar" src="{{item.avatar}}" mode="aspectFill" lazy-load="true"
      style="background-color: {{item.backgroundColor}};" />
    <view class="content">
      <view class="nickname-container">
        <view class="nickname">{{item.nickname}}</view>
        <!-- 删除帖子按钮 - 仅在isOK为真且当前用户是帖子作者时显示 -->
        <view class="delete-post-btn" wx:if="{{isOK && item._openid === openId && !item.isNewPost}}"
          bindtap="deletePost" data-post-id="{{item.id}}" data-sql-id="{{item.sqlId}}">
          删除
        </view>
      </view>
      <!-- 新帖子编辑区域 -->
      <block wx:if="{{item.isNewPost && item.isEditing}}">
        <textarea class="post-edit-textarea" placeholder="写点什么..." auto-focus="true" maxlength="500"
          data-post-id="{{item.id}}" bindinput="onNewPostInput" value="{{item.content}}"></textarea>

        <!-- 图片选择和预览区域 -->
        <view class="post-image-area" wx:if="{{item.tempImages.length > 0 || true}}">
          <!-- 已选择的图片预览 -->
          <view
            class="post-image-preview {{item.tempImages.length === 1 ? 'single-preview' : item.tempImages.length <= 4 ? 'grid-2-preview' : 'grid-3-preview'}}"
            wx:if="{{item.tempImages.length > 0}}">
            <view class="post-image-item" wx:for="{{item.tempImages}}" wx:for-item="imgPath" wx:key="*this"
              wx:for-index="imgIndex">
              <image src="{{imgPath}}" mode="{{item.tempImages.length === 1 ? 'widthFix' : 'aspectFill'}}"
                bindtap="previewSelectedImage" data-post-id="{{item.id}}" data-current="{{imgPath}}" />
              <view class="post-image-delete" bindtap="deleteImage" data-post-id="{{item.id}}"
                data-index="{{imgIndex}}">×</view>
            </view>
          </view>

          <!-- 添加图片按钮 -->
          <view class="post-add-image" bindtap="chooseImage" data-post-id="{{item.id}}"
            wx:if="{{item.tempImages.length < 9}}">
            <view class="post-add-image-icon">+</view>
            <view class="post-add-image-text">添加图片</view>
          </view>
        </view>

        <!-- 标签选择区域 -->
        <view class="post-tag-selector">
          <text class="post-tag-label">选择分区：</text>
          <scroll-view class="post-tag-scroll" scroll-x="true" enhanced="true" show-scrollbar="{{false}}">
            <view class="post-tag-list">
              <view wx:for="{{tagList}}" wx:key="id" wx:if="{{item.id !== 'all' && item.id !== 'announcement'}}"
                class="post-tag-item {{item.id === feedList[0].tag ? 'post-tag-active' : ''}}" bindtap="selectPostTag"
                data-post-id="{{feedList[0].id}}" data-tag-id="{{item.id}}">
                {{item.name}}
              </view>
            </view>
          </scroll-view>
        </view>

        <view class="post-edit-actions">
          <view class="post-edit-cancel" bindtap="cancelNewPost" data-post-id="{{item.id}}">取消</view>
          <view class="post-edit-submit" bindtap="submitNewPost" data-post-id="{{item.id}}">发送</view>
        </view>
      </block>
      <!-- 普通帖子内容 -->
      <block wx:else>
        <text user-select="true" class="text">{{item.content}}</text>

        <!-- 图片布局，根据图片数量自动调整 -->
        <view
          class="images {{item.images.length === 1 ? 'single-image' : item.images.length <= 4 ? 'grid-2' : 'grid-3'}}"
          wx:if="{{item.images.length > 0}}">
          <image wx:for="{{item.images}}" wx:for-item="imageUrl" wx:key="*this" wx:for-index="imgIndex"
            src="{{imageUrl}}" class="image {{item.images.length === 1 ? 'large-image' : ''}}"
            mode="{{item.images.length === 1 ? 'widthFix' : 'aspectFill'}}" bindtap="previewImage"
            data-post-id="{{item.id}}" data-current="{{imageUrl}}"
            data-urls="{{item.images.length > 9 ? item.images.slice(0, 9) : item.images}}" lazy-load="true"
            show-menu-by-longpress="true" />
        </view>
      </block>

      <!-- 时间标签和分类标签放在内容下方，添加评论按钮 -->
      <view class="time-label">
        <view class="time-tag-container">
          <text>{{item.timeLabel}}</text>
          <text class="tag-label" wx:if="{{item.tag}}"> #{{item.tagName}}</text>
        </view>
        <!-- 评论按钮 - 仅在isOK为真时显示 -->
        <view class="comment-btn" wx:if="{{isOK && !item.isNewPost}}" bindtap="showCommentInput"
          data-post-id="{{item.id}}">
          <text class="comment-text">评论</text>
        </view>
      </view>

      <!-- 评论区 -->
      <view class="comment-section" wx:if="{{item.comments && item.comments.length > 0}}">
        <view class="comment-list">
          <!-- 根据显示状态决定显示全部评论还是仅显示前5条 -->
          <block wx:for="{{item.comments}}" wx:for-item="comment" wx:key="index" wx:for-index="commentIndex"
            wx:if="{{item.showAllComments || commentIndex < 5}}">
            <view class="comment-item">
              <view class="comment-main">
                <rich-text user-select="true"
                  nodes="<span class='comment-user'>{{comment.nickname}}</span>: <span class='comment-content'>{{comment.content}}</span>">
                </rich-text>
              </view>
              <!-- 删除评论按钮 - 仅在isOK为真且当前用户是评论作者时显示 -->
              <view class="delete-comment-btn" wx:if="{{isOK && comment._openid === openId}}" bindtap="deleteComment"
                data-post-id="{{item.id}}" data-comment-id="{{comment._id}}">
                删除
              </view>
            </view>
          </block>
        </view>
        <!-- 如果评论超过5条，显示"查看更多"按钮 -->
        <view class="view-more-comments" wx:if="{{item.comments.length > 5 && !item.showAllComments}}"
          bindtap="toggleComments" data-post-id="{{item.id}}">
          查看更多
        </view>
      </view>

      <!-- 评论输入区域 - 仅在isOK为真且当前帖子被选中进行评论时显示 -->
      <view class="comment-input-section" wx:if="{{isOK && item.showCommentInput}}">
        <input class="comment-input" placeholder="写评论..." value="{{item.commentText}}" bindinput="onCommentInput"
          data-post-id="{{item.id}}" data-sql-id="{{item.sqlId}}" confirm-type="send" bindconfirm="submitComment"
          bindblur="onCommentBlur" focus="{{item.showCommentInput}}" />
        <view class="comment-send-btn" bindtap="submitComment" data-post-id="{{item.id}}" data-sql-id="{{item.sqlId}}">
          发送</view>
      </view>
    </view>
  </view>

  <!-- 加载状态指示器 -->
  <view class="loading-container" wx:if="{{isLoading && !isRefreshing}}">
    <view class="loading-indicator"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多内容提示 -->
  <view class="no-more" wx:if="{{!hasMore && feedList.length > 0 && !isLoading}}">
    <text>没有更多内容了</text>
  </view>

  <!-- 空状态提示 -->
  <view class="empty-state" wx:if="{{!isLoading && feedList.length === 0}}">
    <text>暂无内容</text>
  </view>

  <!-- 浮窗按钮 - 定位到顶部并添加新帖子 -->
  <view class="float-btn" wx:if="{{isOK && openId &&lastPostId}}" bindtap="scrollToTopAndAddPost">
    <text class="float-btn-text">+</text>
  </view>
</view>