/**
 * 马卡龙配色模块
 * 根据字符串种子获取马卡龙配色
 */

// 马卡龙配色数组，包含名称和十六进制颜色值
const macaronColors = [
  { name: '浅粉红', color: '#FFD1DC' },
  { name: '薄荷绿', color: '#98FB98' },
  { name: '柠檬黄', color: '#FFFACD' },
  { name: '淡蓝色', color: '#ADD8E6' },
  { name: '薰衣草紫', color: '#E6E6FA' },
  { name: '蜜桃色', color: '#FFDAB9' },
  { name: '米色', color: '#F5F5DC' },
  { name: '草莓粉', color: '#FFB6C1' },
  { name: '棕褐色', color: '#D2B48C' },
  { name: '粉蓝色', color: '#B0E0E6' },
  { name: '浅绿色', color: '#DFF0D8' },
  { name: '樱桃粉', color: '#FFB7C5' },
  { name: '玉米丝色', color: '#FFF8DC' },
  { name: '浅玫瑰色', color: '#FFE4E1' },
  { name: '淡紫色', color: '#CCCCFF' },
  { name: '奶油色', color: '#FFFDD0' },
  { name: '纳瓦霍白', color: '#FFDEAD' },
  { name: '薄荷奶油', color: '#BDFCC9' },
  { name: '梅红色', color: '#DDA0DD' },
  { name: '卡其色', color: '#F0E68C' },
  { name: '蜜瓜色', color: '#F0FFF0' },
  { name: '粉红色', color: '#FFC0CB' },
  { name: '蓟色', color: '#D8BFD8' },
  { name: '浅金菊黄', color: '#FAFAD2' }
];

/**
 * 根据字符串种子获取马卡龙配色
 * @param {int} seed - 字符串种子
 * @returns {Object} 返回包含name和color的对象
 */
function getMacaronColor(seed) {
  // 使用种子值对颜色数组长度取模，确保得到一个有效的索引
  const index = seed % macaronColors.length;

  // 返回颜色对象（包含名称和颜色值）
  return macaronColors[index];
}

module.exports = {
  getMacaronColor
};
