/**
 * 用户相关工具函数
 * 提供用户数据处理、缓存和获取的功能
 */

// 本地存储键名
const STORAGE_KEYS = {
  OPENID: 'user_openid'
};

/**
 * 从本地缓存获取 openId
 * @returns {string|null} 缓存的 openId 或 null（如果缓存不存在）
 */
function getCachedOpenId() {
  try {
    // 获取缓存的 openId
    const openId = wx.getStorageSync(STORAGE_KEYS.OPENID);

    // 如果没有缓存，返回 null
    if (!openId) {
      return null;
    }

    // 返回缓存的 openId（openid理论上是不会变的）
    return openId;
  } catch (error) {
    console.error('获取缓存 openId 失败:', error);
    return null;
  }
}

/**
 * 缓存 openId 到本地存储
 * @param {string} openId - 要缓存的 openId
 */
function cacheOpenId(openId) {
  if (!openId) return;

  try {
    // 保存 openId（openid理论上是不会变的，所以无需设置过期时间）
    wx.setStorageSync(STORAGE_KEYS.OPENID, openId);
  } catch (error) {
    console.error('缓存 openId 失败:', error);
  }
}

/**
 * 获取用户 openId，优先使用缓存
 * @param {Object} pageContext - 页面上下文，用于设置数据
 * @returns {Promise<string|null>} 用户的 openId 或 null（如果获取失败）
 */
function getOpenId(pageContext) {
  return new Promise((resolve, reject) => {
    // 先尝试从缓存获取
    const cachedOpenId = getCachedOpenId();
    if (cachedOpenId) {
      // 如果有缓存，直接使用缓存的值（openid理论上是不会变的）
      if (pageContext) {
        pageContext.setData({ openId: cachedOpenId });
      }
      console.log('使用缓存的 openId:', cachedOpenId);
      resolve(cachedOpenId);
      return;
    }

    // 如果没有缓存，调用云函数获取
    wx.cloud.callFunction({
      name: 'getOpenid',
      success: res => {
        const openId = res.result.openid;

        // 缓存新获取的 openId
        cacheOpenId(openId);

        // 更新页面数据
        if (pageContext) {
          pageContext.setData({ openId: openId });
        }

        console.log('获取新的 openId:', openId);
        resolve(openId);
      },
      fail: err => {
        console.error('获取 openId 失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 检查用户是否有权限执行操作
 * @param {boolean} isOK - 用户权限状态
 * @param {string} errorMessage - 权限不足时显示的错误消息
 * @returns {boolean} 是否有权限
 */
function checkPermission(isOK, errorMessage = '没有权限') {
  if (!isOK) {
    wx.showToast({
      title: errorMessage,
      icon: 'none'
    });
    return false;
  }
  return true;
}

module.exports = {
  getOpenId,
  getCachedOpenId,
  cacheOpenId,
  checkPermission
};
