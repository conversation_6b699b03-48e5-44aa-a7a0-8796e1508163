/**
 * 检查工具
 * 支持本地缓存，只缓存true结果
 */

// 缓存键名
const CACHE_KEY = 'location_check_result';
// 缓存有效期（毫秒），设置为90天
const CACHE_EXPIRATION = 90 * 24 * 60 * 60 * 1000;

/**
 * @returns {Promise<boolean>}
 */
function checkIfOK() {

  return new Promise((resolve) => {
    // 首先尝试从缓存中获取结果
    try {
      const cachedData = wx.getStorageSync(CACHE_KEY);
      if (cachedData) {
        const { result, timestamp } = cachedData;
        const now = Date.now();

        // 检查缓存是否有效（未过期）
        if (now - timestamp < CACHE_EXPIRATION) {
          console.log('使用缓存的检查结果:', result);
          if (result === true) {
            // 只有当缓存结果为true时才使用缓存
            resolve(true);
            return;
          }
          // 如果缓存结果为false，继续进行API请求
        }
      }
    } catch (e) {
      console.error('读取缓存出错:', e);
      // 出错时继续进行API请求
    }

    // 如果没有有效缓存或缓存结果为false，则发起云函数请求
    wx.cloud.callFunction({
      name: 'getIfOK',
      success: res => {
        if (res.result && res.result.isOK !== undefined) {
          const isOK = res.result.isOK;

          // 如果结果为true，则缓存结果
          if (isOK) {
            try {
              wx.setStorageSync(CACHE_KEY, {
                result: true,
                timestamp: Date.now()
              });
              console.log('已缓存检查结果: true');
            } catch (e) {
              console.error('缓存结果出错:', e);
            }
          }

          resolve(isOK);
        } else {
          console.error('Check API调用失败:', res.result.err);
          // 如果API调用失败，默认返回false
          resolve(false);
        }
      },
      fail: () => {
        // 如果请求失败，默认返回false
        resolve(false);
      }
    });
  });
}

module.exports = {
  checkIfOK
};
