/**
 * 帖子服务
 * 提供帖子相关的数据库操作功能
 */

// 本地存储键名
const POST_HISTORY_KEY = 'post_history';

// 加载失败的提示数据
const loadFailedData = [
  {
    id: "System",
    content: '从数据库加载数据失败，请检查网络连接',
    time: Date.now()
  }
];

/**
 * 分页获取信息流数据
 * @param {string} lastPostId - 上一次请求的最后一条记录的ID
 * @param {number} pageSize - 每页数量
 * @param {boolean} isOK - 是否OK
 * @param {string} tag - 分区标签，默认为'all'表示全部
 * @returns {Object} 包含数据和是否有更多的对象
 */
async function getList(lastPostId, pageSize, isOK, tag = 'all') {
  try {
    // 获取原始数据列表
    let rawFeedList;

    // 从数据库获取数据
    try {
      // 获取models对象
      const models = wx.cloud.models;
      if (!models || !models.posts) {
        console.error('数据模型未初始化或posts模型不存在');
        throw new Error('数据模型未初始化');
      }

      // 构建查询条件
      const filter = {
        where: {}
      };

      // 如果有lastPostId，则直接使用该ID进行查询
      if (lastPostId && lastPostId !== '') {
        filter.where.UID = { $lt: lastPostId };
      }

      // 如果指定了分区标签且不是'all'，则添加标签过滤条件
      if (tag && tag !== 'all') {
        filter.where.tag = { $eq: tag };
      }

      // 构建查询参数
      const queryParams = {
        filter: filter,
        select: {
          UID: true,
          content: true,
          images: true,
          tag: true,
          createdAt: true,
          _openid: true,
          comments: {
            content: true,
            _openid: true,
            createdAt: true
          }
        },
        limit: pageSize,
        orderBy: [
          { UID: 'desc' }
        ]
      };

      // 根据isOK选择不同的数据源
      const modelName = isOK ? 'posts' : 'posts_test';
      const data = await models[modelName].list(queryParams);

      // 检查返回的数据格式是否符合预期
      if (data?.data?.records && Array.isArray(data.data.records)) {
        rawFeedList = data.data.records;
      } else {
        console.warn('返回数据格式不符合预期');
        rawFeedList = [];
      }
    } catch (error) {
      console.error('获取数据出错:', error);
      rawFeedList = loadFailedData;
    }

    // 如果没有数据，直接返回空结果
    if (!rawFeedList || !Array.isArray(rawFeedList) || rawFeedList.length === 0) {
      return {
        hasMore: false,
        data: []
      };
    }

    return {
      hasMore: pageSize <= rawFeedList.length,
      data: rawFeedList
    };
  } catch (error) {
    console.error('处理数据出错:', error);
    return {
      hasMore: false,
      data: loadFailedData
    };
  }
}

/**
 * 检查发帖频率限制
 * @returns {Object} 包含是否可以发帖和错误信息的对象
 */
function checkPostRateLimit() {
  try {
    // 获取当前时间
    const now = Date.now();
    // 一分钟的毫秒数
    const ONE_MINUTE = 60 * 1000;

    // 从本地存储获取发帖历史
    let postHistory = wx.getStorageSync(POST_HISTORY_KEY) || [];

    // 过滤出最近一分钟内的发帖记录
    const recentPosts = postHistory.filter(timestamp => now - timestamp < ONE_MINUTE);

    // 如果最近一分钟内已经发了两条或更多帖子，则不允许再发
    if (recentPosts.length >= 2) {
      // 计算需要等待的时间（第一条发帖的时间 + 一分钟 - 当前时间）
      const oldestPost = Math.min(...recentPosts);
      const waitTime = Math.ceil((oldestPost + ONE_MINUTE - now) / 1000);

      return {
        canPost: false,
        message: `发帖太频繁，请等待${waitTime}秒后再试`
      };
    }

    return { canPost: true };
  } catch (error) {
    console.error('检查发帖频率限制出错:', error);
    // 如果出错，默认允许发帖，避免阻止用户正常使用
    return { canPost: true };
  }
}

/**
 * 记录发帖时间
 */
function recordPostTime() {
  try {
    // 获取当前时间
    const now = Date.now();

    // 从本地存储获取发帖历史
    let postHistory = wx.getStorageSync(POST_HISTORY_KEY) || [];

    // 添加新的发帖时间
    postHistory.push(now);

    // 只保留最近的10条记录，避免存储过多数据
    if (postHistory.length > 10) {
      postHistory = postHistory.slice(-10);
    }

    // 更新本地存储
    wx.setStorageSync(POST_HISTORY_KEY, postHistory);
  } catch (error) {
    console.error('记录发帖时间出错:', error);
  }
}

/**
 * 创建帖子（仅文字内容）
 * @param {string} content - 帖子内容
 * @param {string} tag - 帖子标签
 * @param {boolean} isOK - 是否有权限
 * @returns {Promise<Object>} 返回包含成功状态和结果的对象
 */
async function createPost(content, tag, isOK) {
  try {
    // 验证帖子内容
    if (!content || content.trim() === '') {
      return {
        success: false,
        message: '帖子内容不能为空'
      };
    }

    // 检查发帖频率限制
    const rateLimit = checkPostRateLimit();
    if (!rateLimit.canPost) {
      return {
        success: false,
        message: rateLimit.message
      };
    }

    // 获取models对象
    const models = wx.cloud.models;
    if (!models) {
      throw new Error('数据模型未初始化');
    }

    // 根据isOK选择不同的数据源
    const modelName = isOK ? 'posts' : 'posts_test';

    // 构建帖子数据
    const postData = {
      data: {
        content: content,
        tag: tag || 'misc_note', // 默认使用 misc_note 标签
        images: [] // 初始化为空数组
      }
    };

    // 发送数据到数据库
    const result = await models[modelName].create(postData);

    if (result && result.data && result.data.id) {

      return {
        success: true,
        data: result.data,
        message: '发送成功',
        postId: result.data.id
      };
    } else {
      throw new Error('发送失败，返回数据异常');
    }
  } catch (error) {
    console.error('发送帖子失败:', error);
    return {
      success: false,
      message: error.message || '发送失败，请重试',
      error
    };
  }
}

/**
 * 上传图片并更新帖子
 * @param {string} postId - 帖子ID
 * @param {string} uid - 帖子UID
 * @param {Array<string>} images - 图片临时路径数组
 * @param {boolean} isOK - 是否有权限
 * @returns {Promise<Object>} 返回包含成功状态和结果的对象
 */
async function uploadPostImages(postId, uid, images, isOK) {
  try {
    if (!postId || !uid) {
      return {
        success: false,
        message: '帖子ID或UID不能为空'
      };
    }
    console.log('上传图片:', postId, uid, images, isOK);

    if (!Array.isArray(images) || images.length === 0) {
      return {
        success: true,
        message: '没有图片需要上传'
      };
    }

    // 获取models对象
    const models = wx.cloud.models;
    if (!models) {
      throw new Error('数据模型未初始化');
    }

    // 根据isOK选择不同的数据源
    const modelName = isOK ? 'posts' : 'posts_test';

    // 限制图片数量，最多9张
    const limitedImages = images.slice(0, 9);

    // 显示上传中提示
    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });

    // 上传图片到云存储
    const imageUrls = await Promise.all(
      limitedImages.map((path, index) => {
        // 生成云存储路径，格式：/post/UID/index.xxx
        const ext = path.substring(path.lastIndexOf('.'));
        const cloudPath = `post/${uid}/${index}${ext}`;

        console.log('上传图片:', cloudPath, path);

        // 上传文件
        return new Promise((resolve, reject) => {
          wx.cloud.uploadFile({
            cloudPath,
            filePath: path,
            success: res => {
              // 获取图片的URL
              const fileID = res.fileID;
              wx.cloud.getTempFileURL({
                fileList: [fileID],
                success: res => {
                  resolve(res.fileList[0].tempFileURL);
                },
                fail: err => {
                  console.error('获取图片URL失败:', err);
                  reject(err);
                }
              });
            },
            fail: err => {
              console.error('上传图片到云存储失败:', err);
              reject(err);
            }
          });
        });
      })
    );

    // 隐藏上传中提示
    wx.hideLoading();

    // 更新帖子数据，添加图片URL
    if (Array.isArray(imageUrls) && imageUrls.length > 0) {
      const updateResult = await models[modelName].update({
        filter: {
          where: {
            _id: { $eq: postId }
          }
        },
        data: {
          images: imageUrls
        }
      });

      if (updateResult && updateResult.data && updateResult.data.count > 0) {
        return {
          success: true,
          message: '图片上传成功',
          imageUrls
        };
      } else {
        throw new Error('更新帖子图片失败');
      }
    }

    return {
      success: true,
      message: '没有图片需要上传'
    };
  } catch (error) {
    console.error('上传图片失败:', error);
    wx.hideLoading();
    return {
      success: false,
      message: error.message || '上传图片失败，请重试',
      error
    };
  }
}

/**
 * 提交新帖子（包含文字和图片）
 * @param {string} content - 帖子内容
 * @param {string} tag - 帖子标签
 * @param {boolean} isOK - 是否有权限
 * @param {Array<string>} images - 图片临时路径数组，可选
 * @returns {Promise<Object>} 返回包含成功状态和结果的对象
 */
async function addPost(content, tag, isOK, images = []) {
  try {
    // 第一步：创建帖子（仅文字内容）
    const createResult = await createPost(content, tag, isOK);
    console.log('创建帖子结果:', createResult);

    if (!createResult.success) {
      return createResult; // 如果创建帖子失败，直接返回错误
    }

    // 如果没有图片，直接返回创建结果
    if (!Array.isArray(images) || images.length === 0) {
      return createResult;
    }
    //从数据库查询这条帖子uid
    const uid = await getPostUID(createResult.data.id);

    // 第二步：上传图片并更新帖子
    const uploadResult = await uploadPostImages(
      createResult.data.id,
      uid,
      images,
      isOK
    );

    if (!uploadResult.success) {
      // 如果上传图片失败，但帖子已创建，删除帖子
      await deletePost(createResult.data._id, isOK);
      return {
        success: false,
        message: '图片上传失败',
        data: createResult.data
      };
    }

    // 全部成功,记录发帖时间
    recordPostTime();
    return {
      success: true,
      message: '发送成功',
      data: {
        ...createResult.data,
        images: uploadResult.imageUrls || []
      }
    };
  } catch (error) {
    console.error('发送帖子失败:', error);
    return {
      success: false,
      message: error.message || '发送失败，请重试',
      error
    };
  }

  async function getPostUID(postId) {
    const models = wx.cloud.models;
    if (!models) {
      throw new Error('数据模型未初始化');
    }

    // 根据isOK选择不同的数据源
    const modelName = isOK ? 'posts' : 'posts_test';

    const post = await models[modelName].get({
      filter: {
        where: {
          _id: { $eq: postId }
        }
      }
    });

    if (!post || !post.data || !post.data.UID) {
      throw new Error('获取帖子UID失败');
    }

    return post.data.UID;
  }
}

/**
 * 删除帖子
 * @param {string} postId - 帖子ID
 * @param {string} currentOpenId - 当前用户的OpenID
 * @param {boolean} isOK - 是否有权限
 * @returns {Promise<Object>} 返回包含成功状态和结果的对象
 */
async function deletePost(postId, isOK) {
  try {
    // 验证参数
    if (!postId) {
      return {
        success: false,
        message: '帖子ID不能为空'
      };
    }

    // 获取models对象
    const models = wx.cloud.models;
    if (!models) {
      throw new Error('数据模型未初始化');
    }

    // 根据isOK选择不同的数据源
    const modelName = isOK ? 'posts' : 'posts_test';

    // 先获取帖子信息，特别是图片信息
    const post = await models[modelName].get({
      filter: {
        where: {
          _id: { $eq: postId }
        }
      }
    });

    // 检查是否获取到帖子信息
    if (!post || !post.data) {
      return {
        success: false,
        message: '帖子不存在或已被删除'
      };
    }

    // 获取帖子的图片信息
    const images = post.data.images || [];

    // 如果有图片，先删除云存储中的图片
    if (images.length > 0) {
      try {
        console.log('帖子包含图片，准备删除云存储图片:', images);

        // 尝试两种方式删除图片：
        // 1. 直接使用URL中的fileID（如果URL是云存储URL）
        // 2. 根据图片路径规则构造fileID

        // 方法1：尝试从URL中提取fileID
        const fileIDsFromURL = images
          .filter(url => url && url.includes('cloud://'))
          .map(url => {
            try {
              // 如果URL包含cloud://，可能是直接的fileID
              const fileIDPart = url.match(/cloud:\/\/[^?]+/);
              return fileIDPart ? fileIDPart[0] : null;
            } catch (err) {
              return null;
            }
          })
          .filter(fileID => fileID);

        // 方法2：根据图片路径规则构造fileID
        const fileIDsFromPath = images
          .filter(url => url && typeof url === 'string')
          .map(url => {
            try {
              // 检查是否是云存储URL格式
              if (url.includes('tcb.qcloud.la/')) {

                // 提取 envId
                const envMatch = url.match(/\/\/(?:\d+c-)?([a-zA-Z0-9-]+)-\d+\.tcb\.qcloud\.la\//);

                // 提取路径
                const pathMatch = url.match(/tcb\.qcloud\.la(\/[^?#]+)/);

                if (envMatch && envMatch[1] && pathMatch && pathMatch[1]) {
                  const fileID = `cloud://${envMatch[1]}${pathMatch[1]}`;
                  return fileID;
                }
              }
              return null;
            } catch (err) {
              console.error('构造fileID失败:', err, url);
              return null;
            }
          })
          .filter(fileID => fileID);

        // 合并两种方法获取的fileID，去重
        const allFileIDs = [...new Set([...fileIDsFromURL, ...fileIDsFromPath])];

        // 如果有有效的fileID，执行删除操作
        if (allFileIDs.length > 0) {
          console.log('准备删除云存储图片，fileIDs:', allFileIDs);

          try {
            const deleteFileResult = await wx.cloud.deleteFile({
              fileList: allFileIDs
            });
            console.log('删除云存储图片结果:', deleteFileResult);

            // 检查删除结果，记录失败的文件
            if (deleteFileResult && deleteFileResult.fileList) {
              const failedFiles = deleteFileResult.fileList.filter(file => file.status !== 0);
              if (failedFiles.length > 0) {
                console.warn('部分文件删除失败:', failedFiles);
              }
            }
          } catch (error) {
            console.error('删除云存储图片失败:', error);
          }
        }
      } catch (error) {
        // 删除图片失败，记录错误但继续删除帖子
        console.error('删除云存储图片失败:', error);
      }
    }

    // 删除帖子
    const deleteResult = await models[modelName].delete({
      filter: {
        where: {
          _id: { $eq: postId }
        }
      }
    });

    // 检查删除结果
    if (deleteResult && deleteResult.data && deleteResult.data.count > 0) {
      return {
        success: true,
        message: '删除成功'
      };
    } else {
      return {
        success: false,
        message: '删除失败，请重试'
      };
    }
  } catch (error) {
    console.error('删除帖子失败:', error);
    return {
      success: false,
      message: error.message || '删除失败，请重试',
      error
    };
  }
}

module.exports = {
  getList,
  addPost,
  createPost,
  uploadPostImages,
  deletePost
};
