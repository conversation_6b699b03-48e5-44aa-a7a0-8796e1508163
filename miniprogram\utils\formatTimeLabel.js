/**
 * 时间格式化工具函数
 * 将时间戳格式化为友好的时间标签，如"5分钟前"、"2小时前"或具体日期时间
 * @param {number} timestamp - 毫秒级时间戳
 * @returns {string} 格式化后的时间标签
 */
function formatTimeLabel(timestamp) {
  const currentTime = new Date().getTime();
  // 时间戳已经是毫秒级，不需要再转换
  const postTime = timestamp;
  const timeDiff = (currentTime - postTime) / 1000;

  if (timeDiff < 3600) {
    const minutes = Math.floor(timeDiff / 60);
    return `${minutes}分钟前`;
  } else if (timeDiff < 86400) {
    const hours = Math.floor(timeDiff / 3600);
    return `${hours}小时前`;
  } else {
    const postDate = new Date(timestamp);
    const year = postDate.getFullYear();
    const month = postDate.getMonth() + 1;
    const day = postDate.getDate();
    const hours = postDate.getHours().toString().padStart(2, '0');
    const minutes = postDate.getMinutes().toString().padStart(2, '0');
    if (year === new Date().getFullYear()) {
      return `${month}月${day}日 ${hours}:${minutes}`;
    }
    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  }
}

module.exports = {
  formatTimeLabel
};
