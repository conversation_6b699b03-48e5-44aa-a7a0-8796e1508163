/**
 * 评论服务
 * 提供评论相关的数据库操作功能
 */

/**
 * 添加评论
 * @param {string} sqlId - 帖子的SQL ID
 * @param {string} content - 评论内容
 * @param {boolean} isOK - 是否OK
 * @returns {Promise<Object>} 返回包含成功状态和结果的对象
 */
async function addComment(sqlId, content, isOK) {
  try {
    // 获取models对象
    const models = wx.cloud.models;
    if (!models || !models.comments) {
      console.error('数据模型未初始化或comments模型不存在');
      return {
        success: false,
        message: '数据模型未初始化'
      };
    }

    // 记录操作日志
    console.log('添加评论:', sqlId, content, isOK);

    // 构建评论数据
    const commentData = {
      data: {
        post: {    // 关联模型标识
          _id: sqlId, // 关联的数据 ID
        },
        content: content // 评论内容
      },
    };

    // 根据isOK选择不同的数据源
    const modelName = isOK ? 'comments' : 'comments_test';

    // 发送数据到数据库
    const { data } = await models[modelName].create(commentData);

    // 检查结果
    if (data && data.id) {
      console.log('评论添加成功:', data.id);
      return {
        success: true,
        commentId: data.id
      };
    } else {
      console.warn('评论添加结果异常:', data);
      return {
        success: false,
        message: '评论失败，请重试'
      };
    }
  } catch (error) {
    console.error('添加评论失败:', error);
    return {
      success: false,
      message: error.message || '评论失败，请重试',
      error
    };
  }
}

/**
 * 删除评论
 * @param {string} commentId - 评论ID
 * @param {string} currentOpenId - 当前用户的OpenID
 * @param {boolean} isOK - 是否有权限
 * @returns {Promise<Object>} 返回包含成功状态和结果的对象
 */
async function deleteComment(commentId, currentOpenId, isOK) {
  try {
    // 验证参数
    if (!commentId) {
      return {
        success: false,
        message: '评论ID不能为空'
      };
    }

    if (!currentOpenId) {
      return {
        success: false,
        message: '用户ID不能为空'
      };
    }

    // 获取models对象
    const models = wx.cloud.models;
    if (!models) {
      throw new Error('数据模型未初始化');
    }

    // 根据isOK选择不同的数据源
    const modelName = isOK ? 'comments' : 'comments_test';

    // 删除评论
    const deleteResult = await models[modelName].delete({
      filter: {
        where: {
          _id: { $eq: commentId }
        }
      }
    });

    // 检查删除结果
    if (deleteResult && deleteResult.data && deleteResult.data.count > 0) {
      return {
        success: true,
        message: '删除成功'
      };
    } else {
      return {
        success: false,
        message: '删除失败，请重试'
      };
    }
  } catch (error) {
    console.error('删除评论失败:', error);
    return {
      success: false,
      message: error.message || '删除失败，请重试',
      error
    };
  }
}

module.exports = {
  addComment,
  deleteComment
};
