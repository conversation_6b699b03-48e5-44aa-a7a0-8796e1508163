/**
 * 分区标签配置
 * 根据用户权限返回不同的标签配置
 */

// 基础标签配置（未授权用户可见）
const BASE_TAGS = {
  'all': '全部',
  'course': '课程',
  'dormitory': '宿舍',
  'campus_card': '校园卡',
  'mental_health': '心理健康'
};

// 完整标签配置（授权用户可见）
const FULL_TAGS = {
  'all': '全部',
  'misc_note': '叨叨',
  'lost_found': '失物',
  'second_hand': '二手',
  'proxy': '代劳',
  'carpool': '拼车',
  'announcement': '公告'
};

/**
 * 获取标签配置
 * @param {boolean} isAuthorized - 是否已授权
 * @returns {Object} 标签配置对象
 */
function getTagConfig(isAuthorized) {
  return isAuthorized ? FULL_TAGS : BASE_TAGS;
}

/**
 * 获取标签列表
 * @param {boolean} isAuthorized - 是否已授权
 * @returns {Array} 标签列表数组，每项包含id和name
 */
function getTagList(isAuthorized) {
  const tagConfig = getTagConfig(isAuthorized);
  return Object.keys(tagConfig).map(key => ({
    id: key,
    name: tagConfig[key]
  }));
}

module.exports = {
  getTagConfig,
  getTagList,
  BASE_TAGS,
  FULL_TAGS
};
