/**
 * 评论相关工具函数
 * 提供评论处理、提交和删除的功能
 */
const { addComment } = require('../services/commentService.js');
const { stringToInt } = require('./stringToInt.js');
const { getAnimalIcon } = require('./getAnimalIcon.js');
const { getMacaronColor } = require('./getMacaronColor.js');
const { updatePostProperty } = require('./postHelper.js');
const detectSensitiveWords = require('./detectSensitiveWords.js');

/**
 * 提交评论
 * @param {Object} pageContext - 页面上下文
 * @param {string} postId - 帖子ID
 * @param {string} sqlId - 数据库中的帖子ID
 * @param {string} commentText - 评论内容
 * @param {string} openId - 用户openId
 * @param {boolean} isOK - 用户权限状态
 * @returns {Promise<Object>} 提交结果
 */
async function submitCommentHelper(pageContext, postId, sqlId, commentText, openId, isOK) {
  // 验证评论内容
  if (!commentText || commentText.trim() === '') {
    wx.showToast({
      title: '评论内容不能为空',
      icon: 'none'
    });
    return { success: false, message: '评论内容不能为空' };
  }

  // 检测敏感词
  const sensitiveWords = detectSensitiveWords(commentText);
  if (sensitiveWords.length > 0) {
    // 显示敏感词提示弹窗
    wx.showModal({
      title: '内容包含敏感词',
      content: `您的评论中包含以下敏感词：${sensitiveWords.join('、')}，请修改后重试。`,
      showCancel: false,
      confirmText: '确定'
    });
    return { success: false, message: '内容包含敏感词' };
  }

  // 显示加载中
  wx.showLoading({
    title: '发送中...',
    mask: true
  });

  try {
    // 调用评论提交函数
    const { success, commentId } = await addComment(sqlId, commentText, isOK);

    if (success && commentId) {
      // 清空输入框并隐藏评论输入区域
      updatePostProperty(pageContext, postId, 'commentText', '');
      updatePostProperty(pageContext, postId, 'showCommentInput', false);

      // 显示成功提示
      wx.showToast({
        title: '评论成功',
        icon: 'success'
      });

      // 添加新评论到本地数据
      // 使用用户ID作为种子获取动物图标和背景色
      const seed1 = stringToInt(openId + "animalInfo");
      const seed2 = stringToInt(openId + "macaronColor");

      const animalInfo = getAnimalIcon(seed1);
      const macaronColor = getMacaronColor(seed2);

      // 获取当前帖子的评论列表
      const post = pageContext.data.feedList.find(item => item.id === postId);
      if (!post) {
        return { success: true, commentId };
      }

      const updatedComments = [
        ...post.comments,
        {
          _id: commentId,
          content: commentText,
          nickname: macaronColor.name + animalInfo.name,
          createdAt: Date.now(),
          _openid: openId // 添加openid用于权限判断
        }
      ];

      updatePostProperty(pageContext, postId, 'comments', updatedComments);
      return { success: true, commentId };
    } else {
      wx.showToast({
        title: '评论失败，请重试',
        icon: 'none'
      });
      return { success: false, message: '评论失败，请重试' };
    }
  } catch (error) {
    console.error('提交评论失败:', error);
    wx.showToast({
      title: '评论失败，请重试',
      icon: 'none'
    });
    return { success: false, message: error.message || '评论失败，请重试' };
  } finally {
    wx.hideLoading();
  }
}

module.exports = {
  submitCommentHelper
};
