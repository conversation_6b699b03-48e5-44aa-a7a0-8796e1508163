.feed-container {
  padding: 0;
  min-height: 100vh;
  /* 确保容器至少有一个屏幕高度 */
  width: 100%;
  overflow-x: hidden;
  /* 防止页面横向滚动 */
}

/* 固定容器样式 */
.tag-container-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 占位元素，防止内容被固定的分区选择栏遮挡 */
.tag-container-placeholder {
  height: 105rpx;
  /* 与分区选择栏高度一致，包括padding和border */
  width: 100%;
}

/* 分区选择栏样式 - 横向滑动 */
.tag-container {
  white-space: nowrap;
  padding: 16rpx 24rpx;
  background-color: #ffffff;
  overflow-x: auto;
  /* 允许横向滚动 */
  scrollbar-width: none;
  /* 隐藏滚动条 */
  box-sizing: border-box;
  /* 确保padding计入宽度 */
  width: 100%;
}

.tag-scroll-content {
  display: inline-flex;
  flex-wrap: nowrap;
  width: auto;
  /* 自适应内容宽度 */
}

.tag-item {
  display: inline-block;
  padding: 10rpx 20rpx;
  margin: 8rpx;
  font-size: 28rpx;
  color: #333;
  border-radius: 10rpx;
  transition: all 0.3s ease;
  background-color: #f5f5f5;
  white-space: nowrap;
  /* 防止文字换行 */
  flex-shrink: 0;
  /* 防止标签被压缩 */
}

.tag-active {
  color: #576b95;
  font-weight: bold;
  background-color: rgba(87, 107, 149, 0.1);
}

.feed-item {
  display: flex;
  position: relative;
  padding: 20rpx 24rpx;
  margin-bottom: 2rpx;
  background: #ffffff;
  border-radius: 10rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
  /* 平滑过渡效果 */
}

/* 新帖子样式 */
.new-post {
  background-color: #f9f9f9;
  border-left: 4rpx solid #07c160;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  margin-left: 26rpx;
  /* 增大左边距，使对齐 */
  border-radius: 10rpx;
  margin-right: 26rpx;
  /* 减小右边距，使布局更紧凑 */
  margin-top: 4rpx;
  /* 微调头像位置 */
  flex-shrink: 0;
  /* 防止头像被挤压 */
  will-change: transform;
  /* 优化渲染性能 */
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  /* 背景色现在由内联样式控制，使用马卡龙配色 */
}

.content {
  flex: 1;
  overflow: hidden;
  /* 防止内容溢出 */
  padding-top: 2rpx;
  /* 微调内容区顶部间距 */
}

.nickname-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}

.nickname {
  font-weight: bold;
  font-size: 30rpx;
  /* 微调字体大小 */
  line-height: 1.3;
  color: #576b95;
}

/* 删除帖子按钮样式 */
.delete-post-btn {
  font-size: 24rpx;
  color: #999;
  padding: 4rpx 10rpx;
  background-color: #f2f2f2;
  border-radius: 10rpx;
  margin-right: 30rpx;
}

.text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  /* 减小底部间距 */
  line-height: 1.5;
  word-wrap: break-word;
  /* 允许长单词换行 */
  word-break: break-all;
  /* 允许在任意字符间换行 */
  white-space: pre-wrap;
  /* 保留空格和换行符 */
  padding: 2rpx 0;
  /* 减小内边距 */
}

/* 图片布局基础样式 */
.images {
  display: flex;
  flex-wrap: wrap;
  gap: 4rpx;
  margin-top: 10rpx;
  /* 减小顶部间距 */
  width: 100%;
  will-change: transform;
  /* 优化渲染性能 */
}

/* 默认图片样式 */
.image {
  width: 220rpx;
  height: 220rpx;
  border-radius: 10rpx;
  background-color: #f0f0f0;
  object-fit: cover;
  /* 确保图片填充整个容器 */
  will-change: transform;
  /* 优化渲染性能 */
  transition: opacity 0.3s ease;
  /* 平滑过渡效果 */
}

/* 单张图片布局 */
.single-image {
  display: block;
}

.large-image {
  width: 60%;
  max-width: 500rpx;
  max-height: 700rpx;
  height: auto;
  margin-right: auto;
}

/* 2-4张图片布局，一行两张 */
.grid-2 .image {
  width: calc(40.5% - 2rpx);
  height: calc(40.5vw - 50rpx);
  /* 使用视口宽度的一半减去间距和内边距 */
}

/* 5+张图片布局，一行三张 */
.grid-3 .image {
  width: calc(27% - 3rpx);
  height: calc(27vw - 40rpx);
  /* 使用视口宽度的三分之一减去间距和内边距 */
}

/* 评论区样式 */
.comment-section {
  margin-top: 12rpx;
  /* 减小顶部间距 */
  background-color: #f7f7f7;
  border-radius: 10rpx;
  /* 减小圆角 */
  padding: 10rpx 12rpx;
  /* 调整内边距 */
  width: 95%;
  /* 缩窄评论区宽度 */
  box-sizing: border-box;
  /* 确保padding计入宽度 */
}

.comment-list {
  width: 100%;
}

.comment-item {
  font-size: 26rpx;
  line-height: 1.5;
  margin-bottom: 6rpx;
  /* 减小评论项间距 */
  word-wrap: break-word;
  word-break: break-all;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.comment-main {
  flex: 1;
  overflow: hidden;
}

.comment-user {
  color: #576b95;
  font-weight: 500;
  margin-right: 4rpx;
  /* 减小用户名和内容间距 */
}

.comment-content {
  color: #333;
}

/* 删除评论按钮样式 */
.delete-comment-btn {
  font-size: 22rpx;
  color: #999;
  padding: 2rpx 8rpx;
  background-color: #f2f2f2;
  border-radius: 10rpx;
  margin-left: 10rpx;
  flex-shrink: 0;
}

.view-more-comments {
  font-size: 26rpx;
  color: #576b95;
  margin-top: 6rpx;
  /* 减小顶部间距 */
  padding: 2rpx 0;
  /* 减小内边距 */
}

/* 评论输入区域样式 */
.comment-input-section {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  padding: 8rpx 0;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  width: 95%;
  /* 缩窄评论输入区域宽度，与评论区一致 */
  box-sizing: border-box;
  /* 确保padding计入宽度 */
}

.comment-input {
  flex: 1;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  /* 确保padding计入高度 */
  line-height: 60rpx;
  /* 确保文字垂直居中 */
}

.comment-send-btn {
  margin-left: 10rpx;
  height: 60rpx;
  /* 与输入框等高 */
  line-height: 60rpx;
  /* 文字垂直居中 */
  padding: 0 16rpx;
  /* 只设置左右内边距 */
  font-size: 28rpx;
  color: #ffffff;
  background-color: #07c160;
  border-radius: 10rpx;
  min-width: 80rpx;
  text-align: center;
  box-sizing: border-box;
  /* 确保padding计入高度 */
}

/* 新帖子编辑区域样式 */
.post-edit-textarea {
  width: calc(100% - 30rpx);
  /* 减去与评论按钮相同的右侧边距 */
  min-height: 200rpx;
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

/* 图片选择和预览区域 */
.post-image-area {
  width: calc(100% - 30rpx);
  margin-bottom: 20rpx;
}

.post-image-preview {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.single-preview {
  display: block;
}

.grid-2-preview {
  justify-content: flex-start;
}

.grid-3-preview {
  justify-content: flex-start;
}

.post-image-item {
  position: relative;
  margin: 0 20rpx 20rpx 0;
  border-radius: 10rpx;
  overflow: hidden;
}

.single-preview .post-image-item {
  width: 60%;
  max-width: 500rpx;
  max-height: 700rpx;
  height: auto;
  margin-right: auto;
}

.grid-2-preview .post-image-item {
  width: calc(40.5% - 2rpx);
  height: calc(40.5vw - 50rpx);
  /* 使用视口宽度的一半减去间距和内边距 */
}

.grid-3-preview .post-image-item {
  width: calc(27% - 3rpx);
  height: calc(27vw - 40rpx);
  /* 使用视口宽度的三分之一减去间距和内边距 */
}

.post-image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-image-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 54rpx;
  height: 54rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 0 0 0 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
  padding-bottom: 4rpx;
  /* 微调垂直位置 */
  text-align: center;
}

.post-add-image {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 200rpx;
  height: 200rpx;
  border: 1rpx dashed #ccc;
  border-radius: 10rpx;
  color: #999;
}

.post-add-image-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.post-add-image-text {
  font-size: 24rpx;
}

/* 标签选择区域样式 */
.post-tag-selector {
  margin-bottom: 20rpx;
  width: calc(100% - 30rpx);
  /* 与编辑区域保持一致的宽度 */
}

.post-tag-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.post-tag-scroll {
  width: 100%;
  white-space: nowrap;
}

.post-tag-list {
  display: inline-flex;
  flex-wrap: nowrap;
}

.post-tag-item {
  display: inline-block;
  padding: 8rpx 16rpx;
  margin-right: 10rpx;
  font-size: 26rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.post-tag-active {
  color: #ffffff;
  background-color: #07c160;
}

.post-edit-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
  padding-right: 30rpx;
  /* 与评论按钮的右侧边距保持一致 */
}

.post-edit-cancel {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f2f2f2;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.post-edit-submit {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #07c160;
  border-radius: 10rpx;
}

/* 时间标签样式，固定在内容下方 */
.time-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
  /* 减小顶部间距 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 两端对齐，时间在左，评论按钮在右 */
}

.time-tag-container {
  display: flex;
  align-items: center;
}

.tag-label {
  color: #999;
  /* 与时间标签相同的颜色 */
  margin-left: 8rpx;
}

/* 评论按钮样式 */
.comment-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  background-color: #f2f2f2;
  border-radius: 10rpx;
  margin-right: 30rpx;
  /* 右边与内容区域对齐 */
}

.comment-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 400;
}

/* 加载状态指示器样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-indicator {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #576b95;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 没有更多内容提示 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}

/* 空状态提示 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  font-size: 28rpx;
  color: #999;
}

/* 浮窗按钮样式 */
.float-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #07c160;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: all 0.3s ease;
}

/* 浮窗按钮中的加号文本样式 */
.float-btn-text {
  font-size: 60rpx;
  font-weight: bold;
  line-height: 1;
  position: relative;
  top: -2rpx;
  /* 微调位置，使加号在视觉上居中 */
}

.float-btn:active {
  transform: scale(0.95);
  background-color: #06ad56;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {

  /* 暗黑模式下分区选择栏样式 */
  .tag-container-fixed {
    background-color: #1a1a1a;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  }

  .tag-container {
    background-color: #1a1a1a;
  }

  .tag-item {
    color: #eee;
    background-color: #2a2a2a;
  }

  .tag-active {
    color: #7f9bcd;
    background-color: rgba(127, 155, 205, 0.15);
  }

  .tag-label {
    color: #bbb;
    /* 与暗黑模式下时间标签相同的颜色 */
  }

  .feed-item {
    background: #1a1a1a;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
  }

  .nickname {
    color: #7f9bcd;
  }

  .text {
    color: #eee;
    /* 暗黑模式下正文字体颜色变浅，增强对比度 */
  }

  .time-label {
    color: #bbb;
    /* 时间标签文字颜色调整 */
  }

  /* 暗黑模式下评论区样式 */
  .comment-section {
    background-color: #2a2a2a;
  }

  .comment-user {
    color: #7f9bcd;
  }

  .comment-content {
    color: #eee;
  }

  .view-more-comments {
    color: #7f9bcd;
  }

  /* 暗黑模式下评论输入区域样式 */
  .comment-input-section {
    border-top: 1rpx solid rgba(255, 255, 255, 0.05);
  }

  .comment-input {
    background-color: #2a2a2a;
    color: #eee;
  }

  .comment-send-btn {
    color: #ffffff;
    background-color: #07c160;
  }

  /* 暗黑模式下评论按钮样式 */
  .comment-btn {
    background-color: #2a2a2a;
  }

  .comment-text {
    color: #eee;
  }

  .avatar {
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    /* 减小边框透明度 */
  }

  .image {
    background-color: #333;
    /* 暗黑模式下图片背景色 */
  }

  /* 暗黑模式下单张大图的样式 */
  .large-image {
    background-color: #333;
  }

  /* 暗黑模式下加载指示器样式 */
  .loading-indicator {
    border: 4rpx solid #333;
    border-top: 4rpx solid #7f9bcd;
  }

  .loading-text,
  .no-more text,
  .empty-state text {
    color: #bbb;
  }

  /* 暗黑模式下浮窗按钮样式 */
  .float-btn {
    background-color: #07c160;
    color: #ffffff;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.4);
  }

  /* 暗黑模式下浮窗按钮中的加号文本样式 */
  .float-btn-text {
    color: #ffffff;
  }

  .float-btn:active {
    background-color: #06ad56;
  }

  /* 暗黑模式下新帖子样式 */
  .new-post {
    background-color: #222;
    border-left: 4rpx solid #07c160;
  }

  /* 暗黑模式下删除按钮样式 */
  .delete-post-btn,
  .delete-comment-btn {
    color: #aaa;
    background-color: #333;
  }

  .post-edit-textarea {
    background-color: #2a2a2a;
    border: 1rpx solid #444;
    color: #eee;
    width: calc(100% - 30rpx);
    /* 保持与亮色模式一致的宽度 */
  }

  /* 暗黑模式下图片选择和预览区域 */
  .post-add-image {
    border-color: #444;
    color: #aaa;
    background-color: #2a2a2a;
  }

  .post-tag-label {
    color: #bbb;
  }

  .post-tag-item {
    color: #eee;
    background-color: #333;
  }

  .post-tag-active {
    color: #ffffff;
    background-color: #07c160;
  }

  .post-edit-cancel {
    color: #eee;
    background-color: #444;
  }

  .post-edit-submit {
    background-color: #07c160;
  }
}