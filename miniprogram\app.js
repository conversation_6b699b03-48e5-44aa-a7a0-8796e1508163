// app.js
const { init } = require('./sdk/wxCloudClientSDK.umd.js')

App({
  onLaunch: function () {
    // 初始化云开发
    try {
      init(wx.cloud);
    } catch (e) {
      console.error("云开发初始化失败", e);
    }
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      // 指定云开发环境 ID
      wx.cloud.init({
        env: "cloudbase-1g1qycyq463db934",
        traceUser: false,
      });
    }
  },
});
