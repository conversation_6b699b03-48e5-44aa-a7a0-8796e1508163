/**
 * 首页 - 信息流展示与交互
 */
const { getList, addPost, deletePost } = require('../../services/postService.js');
const { deleteComment } = require('../../services/commentService.js');
const { checkIfOK } = require('../../utils/checkIfOK.js');
const { getTagConfig, getTagList } = require('../../config/tags.js');
const { findPostIndex, updatePostProperty, processPosts } = require('../../utils/postHelper.js');
const { stringToInt } = require('../../utils/stringToInt.js');
const { getAnimalIcon } = require('../../utils/getAnimalIcon.js');
const { getMacaronColor } = require('../../utils/getMacaronColor.js');
const { getOpenId, checkPermission } = require('../../utils/userHelper.js');
const { submitCommentHelper } = require('../../utils/commentHelper.js');
const detectSensitiveWords = require('../../utils/detectSensitiveWords.js');

// 分区标签配置（动态更新）
let TAG_MAP = getTagConfig(false);

Page({
  /**
   * 页面的初始数据
   */
  data: {
    feedList: [],          // 信息流列表
    lastPostId: '',        // 上一次加载的最后一条帖子ID
    pageSize: 20,          // 每页加载数量
    hasMore: true,         // 是否有更多数据
    isLoading: false,      // 加载状态标志
    isRefreshing: false,   // 刷新状态标志
    isOK: false,           // 权限状态
    currentTag: 'all',     // 当前选中的分区标签
    currentTagName: '全部', // 当前选中的分区标签名称
    tagList: getTagList(false), // 所有分区标签列表
    openId: ''
  },

  // 防止重复提交的标志
  isSubmitting: false,

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    try {
      // 检查用户权限
      //const isOK = false;
      const isOK = await checkIfOK();
      this.setData({ isOK });

      if (isOK) {
        // 更新分区标签配置
        TAG_MAP = getTagConfig(true);
        this.setData({
          tagList: getTagList(true)
        });

        // 获取用户 openId（使用缓存机制）
        getOpenId(this);
      }

      // 加载初始数据
      this.loadMore();
    } catch (error) {
      console.error('页面加载失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 加载更多数据
   * @param {Function} callback - 加载完成后的回调函数
   * @returns {Promise<void>}
   */
  async loadMore(callback) {
    // 如果没有更多数据或正在加载中，直接返回
    if (!this.data.hasMore || this.data.isLoading) return;

    // 设置加载状态
    this.setData({ isLoading: true });

    try {
      const { lastPostId, pageSize, isOK, currentTag } = this.data;

      // 获取数据
      const { data, hasMore } = await getList(lastPostId, pageSize, isOK, currentTag);

      // 使用帮助函数处理数据
      const updated = processPosts(data, TAG_MAP);

      // 更新数据
      this.setData({
        feedList: lastPostId === '' ? updated : [...this.data.feedList, ...updated],
        lastPostId: updated.length > 0 ? updated[updated.length - 1].id : lastPostId,
        hasMore,
        isLoading: false,
        isRefreshing: false
      }, () => {
        if (typeof callback === 'function') callback();
      });
    } catch (error) {
      console.error('加载数据失败:', error);

      // 显示错误提示
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });

      // 出错时也要重置加载状态
      this.setData({
        isLoading: false,
        isRefreshing: false
      }, () => {
        if (typeof callback === 'function') callback();
      });
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMore();
  },

  /**
   * 页面下拉刷新事件的处理函数
   */
  onPullDownRefresh() {
    // 如果已经在刷新中，则不重复操作
    if (this.data.isRefreshing) return;

    // 设置刷新状态并重置列表参数
    this.setData({
      isRefreshing: true,
      lastPostId: '',
      hasMore: true
    });

    // 重新加载数据
    this.loadMore(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 预览帖子图片
   * @param {Object} e - 事件对象
   */
  previewImage(e) {
    const { current, urls, postId } = e.currentTarget.dataset;

    // 如果直接提供了urls数组，直接使用
    if (urls) {
      wx.previewImage({
        current, // 当前显示图片的链接
        urls // 需要预览的图片链接列表
      });
      return;
    }

    // 否则从帖子中获取图片数组
    if (postId) {
      const index = findPostIndex(this.data.feedList, postId);
      if (index === -1) return;

      const post = this.data.feedList[index];

      // 确保图片数组存在
      if (!post.images || !Array.isArray(post.images) || post.images.length === 0) return;

      // 限制预览的图片数量为9张
      const imageUrls = post.images.length > 9 ? post.images.slice(0, 9) : post.images;

      wx.previewImage({
        current, // 当前显示图片的链接
        urls: imageUrls // 需要预览的图片链接列表
      });
    }
  },

  /**
 * 用户点击右上角分享
 */
  onShareAppMessage() {
    return {
      title: 'The漳墙墙',
      path: '/pages/index/index'
    };
  },

  /**
   * 用户分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: 'The漳墙墙'
    };
  },

  /**
   * 切换评论显示状态
   * @param {Object} e - 事件对象
   */
  toggleComments(e) {
    const { postId } = e.currentTarget.dataset;
    const index = findPostIndex(this.data.feedList, postId);

    if (index !== -1) {
      // 使用帮助函数更新帖子属性
      updatePostProperty(
        this,
        postId,
        'showAllComments',
        !this.data.feedList[index].showAllComments
      );
    }
  },

  /**
   * 切换分区标签
   * @param {Object} e - 事件对象
   */
  switchTag(e) {
    const tagId = e.currentTarget.dataset.tagId;

    // 如果点击的是当前选中的标签，不做任何操作
    if (tagId === this.data.currentTag) {
      return;
    }

    // 获取标签名称
    const tagName = TAG_MAP[tagId];
    if (!tagName) {
      console.error('未找到标签名称:', tagId);
      wx.showToast({
        title: '标签不存在',
        icon: 'none'
      });
      return;
    }

    // 更新当前选中的标签，并重置列表数据
    this.setData({
      currentTag: tagId,
      currentTagName: tagName,
      feedList: [],
      lastPostId: '',
      hasMore: true,
      isLoading: false // 确保加载状态被重置
    }, () => {
      // 重新加载数据
      this.loadMore();
    });
  },

  /**
   * 评论输入框内容变化处理
   * @param {Object} e - 事件对象
   */
  onCommentInput(e) {
    const { postId } = e.currentTarget.dataset;
    const value = e.detail.value;

    // 使用帮助函数更新帖子属性
    updatePostProperty(this, postId, 'commentText', value);
  },

  /**
   * 显示评论输入框
   * @param {Object} e - 事件对象
   */
  showCommentInput(e) {
    // 检查权限
    if (!checkPermission(this.data.isOK)) {
      return;
    }

    const { postId } = e.currentTarget.dataset;

    // 使用帮助函数更新帖子属性
    updatePostProperty(this, postId, 'showCommentInput', true);
  },

  /**
   * 评论输入框失去焦点时处理
   * @param {Object} e - 事件对象
   */
  onCommentBlur(e) {
    const { postId } = e.currentTarget.dataset;
    const index = findPostIndex(this.data.feedList, postId);

    if (index !== -1) {
      const post = this.data.feedList[index];
      // 如果输入框内容为空，则隐藏输入框
      if (!post.commentText || post.commentText.trim() === '') {
        updatePostProperty(this, postId, 'showCommentInput', false);
      }
    }
  },

  /**
   * 提交评论
   * @param {Object} e - 事件对象
   */
  async submitComment(e) {
    // 检查权限
    if (!checkPermission(this.data.isOK)) {
      return;
    }

    const { postId, sqlId } = e.currentTarget.dataset;
    const index = findPostIndex(this.data.feedList, postId);

    if (index === -1) return;

    const post = this.data.feedList[index];
    const commentText = post.commentText;

    // 使用评论工具函数提交评论
    await submitCommentHelper(this, postId, sqlId, commentText, this.data.openId, this.data.isOK);
  },

  /**
   * 删除帖子
   * @param {Object} e - 事件对象
   */
  async deletePost(e) {
    // 检查权限
    if (!checkPermission(this.data.isOK)) {
      return;
    }

    const { postId, sqlId } = e.currentTarget.dataset;

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条帖子吗？删除后无法恢复。',
      confirmColor: '#FF0000',
      success: async (res) => {
        if (res.confirm) {
          // 用户点击确定，执行删除操作
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          try {
            // 调用删除帖子函数
            const result = await deletePost(sqlId, this.data.isOK);

            if (result.success) {
              // 从本地列表中移除帖子
              const index = findPostIndex(this.data.feedList, postId);
              if (index !== -1) {
                const updatedFeedList = [...this.data.feedList];
                updatedFeedList.splice(index, 1);
                this.setData({
                  feedList: updatedFeedList
                });
              }

              // 显示成功提示
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: result.message || '删除失败，请重试',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('删除帖子失败:', error);
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },

  /**
   * 删除评论
   * @param {Object} e - 事件对象
   */
  async deleteComment(e) {
    // 检查权限
    if (!checkPermission(this.data.isOK)) {
      return;
    }

    const { postId, commentId } = e.currentTarget.dataset;

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条评论吗？删除后无法恢复。',
      confirmColor: '#FF0000',
      success: async (res) => {
        if (res.confirm) {
          // 用户点击确定，执行删除操作
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          try {
            // 调用删除评论函数
            const result = await deleteComment(commentId, this.data.openId, this.data.isOK);

            if (result.success) {
              // 从本地列表中移除评论
              const postIndex = findPostIndex(this.data.feedList, postId);
              if (postIndex !== -1) {
                const post = this.data.feedList[postIndex];
                const commentIndex = post.comments.findIndex(comment => comment._id === commentId);

                if (commentIndex !== -1) {
                  const updatedComments = [...post.comments];
                  updatedComments.splice(commentIndex, 1);
                  updatePostProperty(this, postId, 'comments', updatedComments);
                }
              }

              // 显示成功提示
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: result.message || '删除失败，请重试',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('删除评论失败:', error);
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },

  /**
   * 滚动到顶部并添加一条新帖子供用户编辑发送
   */
  scrollToTopAndAddPost() {
    // 检查权限
    if (!checkPermission(this.data.isOK)) {
      return;
    }

    // 检查是否已经有一个正在编辑的新帖子
    const existingNewPost = this.data.feedList.find(post => post.isNewPost && post.isEditing);
    if (existingNewPost) {
      // 如果已经有正在编辑的新帖子，则只滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });

      wx.showToast({
        title: '请完成当前编辑',
        icon: 'none'
      });
      return;
    }

    // 滚动到顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });

    // 使用用户ID作为种子获取动物图标和背景色
    const postOpenid = this.data.openId;
    const seed1 = stringToInt(postOpenid + "animalInfo");
    const seed2 = stringToInt(postOpenid + "macaronColor");

    const animalInfo = getAnimalIcon(seed1);
    const macaronColor = getMacaronColor(seed2);

    // 默认使用 misc_note 标签，而不是 all
    let defaultTag = 'misc_note';
    let defaultTagName = TAG_MAP[defaultTag] || '叨叨';

    // 如果当前选中的标签不是 all 且不是 announcement，则使用当前选中的标签
    if (this.data.currentTag !== 'all' && this.data.currentTag !== 'announcement') {
      defaultTag = this.data.currentTag;
      defaultTagName = this.data.currentTagName;
    }

    // 创建一个临时的新帖子对象
    const newPost = {
      id: 'new_post_', // 生成一个临时ID
      sqlId: 'new_post_', // 暂时为空，发送成功后会获取真实ID
      content: '', // 空内容，等待用户输入
      images: [], // 暂无图片
      tempImages: [], // 临时图片路径，用于上传前预览
      tag: defaultTag, // 默认使用 misc_note 标签
      time: Date.now(), // 当前时间
      timeLabel: '刚刚', // 时间标签
      comments: [], // 暂无评论
      showAllComments: false,
      commentText: '',
      showCommentInput: false,
      tagName: defaultTagName, // 标签名称
      avatar: animalInfo.path, // 使用动物图标作为头像
      nickname: macaronColor.name + animalInfo.name + "的新帖子", // 新帖子
      backgroundColor: macaronColor.color, // 使用马卡龙颜色作为背景色
      isNewPost: true, // 标记为新帖子
      isEditing: true // 标记为正在编辑状态
    };

    // 将新帖子添加到列表顶部
    this.setData({
      feedList: [newPost, ...this.data.feedList]
    });
  },

  /**
   * 选择帖子标签
   * @param {Object} e - 事件对象
   */
  selectPostTag(e) {
    const { postId, tagId } = e.currentTarget.dataset;

    // 禁止选择"公告"标签
    if (tagId === 'announcement') {
      wx.showToast({
        title: '不能选择公告标签',
        icon: 'none'
      });
      return;
    }

    const index = findPostIndex(this.data.feedList, postId);

    if (index !== -1) {
      // 更新帖子标签
      updatePostProperty(this, postId, 'tag', tagId);
      updatePostProperty(this, postId, 'tagName', TAG_MAP[tagId] || '');
    }
  },

  /**
   * 处理新帖子输入
   * @param {Object} e - 事件对象
   */
  onNewPostInput(e) {
    const { postId } = e.currentTarget.dataset;
    const value = e.detail.value;

    // 使用帮助函数更新帖子属性
    updatePostProperty(this, postId, 'content', value);
  },

  /**
   * 取消新帖子
   * @param {Object} e - 事件对象
   */
  cancelNewPost(e) {
    const { postId } = e.currentTarget.dataset;
    const index = findPostIndex(this.data.feedList, postId);

    if (index !== -1) {
      // 从列表中移除新帖子
      const updatedFeedList = [...this.data.feedList];
      updatedFeedList.splice(index, 1);
      this.setData({
        feedList: updatedFeedList
      });
    }
  },

  /**
   * 选择图片
   * @param {Object} e - 事件对象
   */
  chooseImage(e) {
    const { postId } = e.currentTarget.dataset;
    const index = findPostIndex(this.data.feedList, postId);

    if (index === -1) return;

    const post = this.data.feedList[index];

    // 计算还可以选择的图片数量（最多9张）
    const count = 9 - post.tempImages.length;

    if (count <= 0) {
      wx.showToast({
        title: '最多只能上传9张图片',
        icon: 'none'
      });
      return;
    }

    // 调用微信选择图片API
    wx.chooseMedia({
      count,
      sizeType: ['compressed'], // 压缩图片
      mediaType: ['image'], // 只选择图片
      sourceType: ['album', 'camera'], // 相册和相机
      success: async (res) => {
        // 获取选择的图片临时路径
        const tempFilePaths = res.tempFiles.map(file => file.tempFilePath);

        if (!tempFilePaths || tempFilePaths.length === 0) return;

        // 过滤非图片文件
        const validFilePaths = tempFilePaths.filter(filePath => {
          const ext = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase();
          return ['jpg', 'jpeg', 'png', 'gif'].includes(ext);
        });

        console.log('过滤后的图片:', validFilePaths);

        // 先检查并压缩大于1MB的图片
        let processedImages = [...validFilePaths];
        try {
          const fs = wx.getFileSystemManager();
          for (let i = 0; i < validFilePaths.length; i++) {
            const tempFilePath = validFilePaths[i];

            const fileInfoRes = await new Promise((resolve, reject) => {
              fs.getFileInfo({
                filePath: tempFilePath,
                success: res => resolve(res),
                fail: err => reject(err)
              });
            });

            if (fileInfoRes.size > 1024 * 1024) {
              // 调用微信压缩API
              const compressResult = await wx.compressImage({
                src: tempFilePath,
                quality: 80
              });
              // 替换为压缩后的图片路径
              const index = processedImages.indexOf(tempFilePath);
              if (index !== -1) {
                processedImages[index] = compressResult.tempFilePath;
              }
            }
          }
        } catch (error) {
          console.error('压缩图片失败:', error);
        }

        // 检查和压缩完成后，再更新帖子的临时图片数组
        const updatedTempImages = [...post.tempImages, ...processedImages];

        // 使用帮助函数更新帖子属性
        updatePostProperty(this, postId, 'tempImages', updatedTempImages);
      }
    });
  },

  /**
   * 删除选择的图片
   * @param {Object} e - 事件对象
   */
  deleteImage(e) {
    const { postId, index } = e.currentTarget.dataset;
    const postIndex = findPostIndex(this.data.feedList, postId);

    if (postIndex === -1) return;

    const post = this.data.feedList[postIndex];

    // 复制临时图片数组并删除指定索引的图片
    const updatedTempImages = [...post.tempImages];
    updatedTempImages.splice(index, 1);

    // 使用帮助函数更新帖子属性
    updatePostProperty(this, postId, 'tempImages', updatedTempImages);
  },

  /**
   * 预览选择的图片
   * @param {Object} e - 事件对象
   */
  previewSelectedImage(e) {
    const { postId, current } = e.currentTarget.dataset;
    const postIndex = findPostIndex(this.data.feedList, postId);

    if (postIndex === -1) return;

    const post = this.data.feedList[postIndex];

    // 调用微信预览图片API
    wx.previewImage({
      current, // 当前显示图片的链接
      urls: post.tempImages // 需要预览的图片链接列表
    });
  },

  /**
   * 提交新帖子
   * @param {Object} e - 事件对象
   */
  async submitNewPost(e) {
    const { postId } = e.currentTarget.dataset;
    const index = findPostIndex(this.data.feedList, postId);

    if (index === -1) return;

    const post = this.data.feedList[index];
    const content = post.content;

    // 验证帖子内容
    if (!content || content.trim() === '') {
      wx.showToast({
        title: '帖子内容不能为空',
        icon: 'none'
      });
      return;
    }

    // 检测敏感词
    const sensitiveWords = detectSensitiveWords(content);
    if (sensitiveWords.length > 0) {
      // 显示敏感词提示弹窗
      wx.showModal({
        title: '内容包含敏感词',
        content: `您的帖子中包含以下敏感词：${sensitiveWords.join('、')}，请修改后重试。`,
        showCancel: false,
        confirmText: '确定'
      });
      return;
    }

    // 防止重复提交
    if (this.isSubmitting) {
      return;
    }
    this.isSubmitting = true;

    // 显示加载中
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    try {
      // 调用发帖工具函数，传入临时图片数组
      const result = await addPost(content, post.tag, this.data.isOK, post.tempImages);

      if (result.success) {
        // 发送成功，移除临时帖子
        const updatedFeedList = [...this.data.feedList];
        updatedFeedList.splice(index, 1);

        // 显示成功提示
        wx.showToast({
          title: result.message || '发送成功',
          icon: 'success'
        });

        // 刷新列表以获取新帖子
        this.setData({
          feedList: updatedFeedList,
          isRefreshing: true,
          lastPostId: '',
          hasMore: true
        }, () => {
          this.loadMore();
        });
      } else {
        throw new Error(result.message || '发送失败，请重试');
      }
    } catch (error) {
      console.error('发送帖子失败:', error);
      // 显示失败提示,需要确定
      wx.showModal({
        title: '发送失败',
        content: error.message || '发送失败，请重试',
        showCancel: false,
        confirmText: '确定'
      });
    } finally {
      wx.hideLoading();
      // 重置提交状态
      setTimeout(() => {
        this.isSubmitting = false;
      }, 1000); // 1秒后重置，防止快速连续点击
    }
  }
});