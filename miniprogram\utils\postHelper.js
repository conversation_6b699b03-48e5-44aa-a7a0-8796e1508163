/**
 * 帖子处理工具函数
 * 提供帖子数据处理、查找和更新的功能
 */
const { formatTimeLabel } = require('./formatTimeLabel.js');
const { stringToInt } = require('./stringToInt.js');
const { getAnimalIcon } = require('./getAnimalIcon.js');
const { getMacaronColor } = require('./getMacaronColor.js');

/**
 * 查找帖子索引
 * @param {Array} feedList - 帖子列表
 * @param {string} postId - 帖子ID
 * @returns {number} 帖子在列表中的索引，未找到返回-1
 */
function findPostIndex(feedList, postId) {
  return feedList.findIndex(item => item.id === postId);
}

/**
 * 更新帖子特定属性
 * @param {Page} pageContext - 页面上下文
 * @param {string} postId - 帖子ID
 * @param {string} property - 要更新的属性名
 * @param {any} value - 新的属性值
 * @returns {boolean} 是否更新成功
 */
function updatePostProperty(pageContext, postId, property, value) {
  const feedList = pageContext.data.feedList;
  const index = findPostIndex(feedList, postId);

  if (index === -1) return false;

  const key = `feedList[${index}].${property}`;
  pageContext.setData({ [key]: value });
  return true;
}

/**
 * 处理评论数据
 * @param {Array} comments - 原始评论数据
 * @returns {Array} 处理后的评论数据
 */
function processComments(comments) {
  if (!comments || !Array.isArray(comments) || comments.length === 0) {
    return [];
  }

  // 按时间排序评论，最新的在下面
  comments.sort((a, b) => a.createdAt - b.createdAt);

  // 为每条评论添加昵称
  return comments.map(comment => {
    const openid = comment._openid || "anonymous";
    // 使用 openid 作为种子生成动物图标和颜色
    const animalInfo = getAnimalIcon(stringToInt(openid + "animalInfo"));
    const macaronColor = getMacaronColor(stringToInt(openid + "macaronColor"));

    return {
      ...comment,
      nickname: macaronColor.name + animalInfo.name
    };
  });
}

/**
 * 处理帖子数据
 * @param {Array} posts - 原始帖子数据
 * @param {Object} tagConfig - 标签配置
 * @returns {Array} 处理后的帖子数据
 */
function processPosts(posts, tagConfig) {
  return posts.map(item => {
    if (!item || !item.UID) {
      console.warn('数据项缺少ID:', item);
      return null;
    }

    // 使用用户ID作为种子获取动物图标和背景色
    const postOpenid = item._openid;
    const seed1 = stringToInt(postOpenid + "animalInfo");
    const seed2 = stringToInt(postOpenid + "macaronColor");

    const animalInfo = getAnimalIcon(seed1);
    const macaronColor = getMacaronColor(seed2);

    // 初始化帖子数据
    const processedItem = {
      ...item,
      id: item.UID,
      sqlId: item._id,
      content: item.content,
      images: Array.isArray(item.images) ? item.images : [],
      tag: item.tag || 'misc_note',
      time: item.createdAt,
      comments: Array.isArray(item.comments) ? item.comments : [],
      avatar: animalInfo.path,
      nickname: macaronColor.name + animalInfo.name,
      backgroundColor: macaronColor.color
    };

    // 添加时间标签
    processedItem.timeLabel = formatTimeLabel(processedItem.time);

    // 添加标签名称
    if (processedItem.tag && tagConfig[processedItem.tag]) {
      processedItem.tagName = tagConfig[processedItem.tag];
    }

    // 处理评论数据
    processedItem.comments = processComments(processedItem.comments);

    // 初始状态设置
    processedItem.showAllComments = false;
    processedItem.commentText = '';
    processedItem.showCommentInput = false;

    return processedItem;
  }).filter(Boolean); // 过滤掉无效的数据项
}

module.exports = {
  findPostIndex,
  updatePostProperty,
  processComments,
  processPosts
};
